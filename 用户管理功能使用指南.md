# 用户管理功能使用指南

## 🎯 功能概述

已成功实现完整的用户管理功能，包括：
- ✅ 从数据库获取真实用户数据
- ✅ 用户列表展示（支持搜索、筛选、分页）
- ✅ 添加新用户
- ✅ 编辑用户信息
- ✅ 删除用户
- ✅ 权限控制（仅管理员可访问）

## 🗄️ 数据库结构

用户数据存储在 `users` 表中，主要字段：
- `id`: 用户ID（主键）
- `username`: 用户名
- `email`: 邮箱
- `phone`: 手机号
- `sys_access`: 系统权限（admin/guest）
- `enabled`: 是否启用
- `first_name`: 名
- `last_name`: 姓
- `created_at`: 创建时间

## 🚀 启动步骤

### 1. 准备数据库
```bash
# 确保MySQL数据库运行
# 执行初始化脚本
mysql -u root -p < server/database/init.sql

# 可选：插入示例数据
mysql -u root -p < server/database/sample_users.sql
```

### 2. 启动后端服务
```bash
cd server
npm start
```

### 3. 启动前端服务
```bash
cd web_0612
npm start
```

### 4. 访问用户管理页面
- 以管理员身份登录系统
- 在左侧菜单中点击"用户管理"
- 或直接访问 `/admin` 路径

## 🔧 API接口

### 获取用户列表
```
GET /api/admin/users
参数：
- page: 页码（默认1）
- pageSize: 每页数量（默认10）
- search: 搜索关键词
- role: 角色筛选
- status: 状态筛选
```

### 获取单个用户
```
GET /api/admin/users/:id
```

### 创建用户
```
POST /api/admin/users
Body: {
  "name": "用户姓名",
  "username": "用户名",
  "email": "邮箱",
  "phone": "手机号",
  "role": "角色",
  "password": "密码"
}
```

### 更新用户
```
PUT /api/admin/users/:id
Body: {
  "name": "用户姓名",
  "username": "用户名",
  "email": "邮箱",
  "phone": "手机号",
  "role": "角色",
  "enabled": true/false
}
```

### 删除用户
```
DELETE /api/admin/users/:id
```

## 🎨 前端功能

### 用户列表页面
- **搜索功能**: 支持按姓名、用户名、邮箱搜索
- **筛选功能**: 
  - 角色筛选（Administrator/Developer/Manager）
  - 状态筛选（Active/Inactive）
  - 日期排序
- **分页显示**: 每页10条记录
- **操作按钮**: 编辑、删除

### 添加用户
- 姓名、用户名、邮箱（必填）
- 手机号、密码、角色选择
- 表单验证

### 编辑用户
- 修改用户基本信息
- 角色权限调整
- 启用/禁用状态

## 🔐 权限控制

- 只有具有管理员权限（`sys_access = 'admin'`）的用户才能访问用户管理功能
- 前端路由配置了 `access: 'isAdmin'` 权限检查
- 后端API使用 `requireRole('admin')` 中间件验证权限

## 🧪 测试

运行测试脚本验证API功能：
```bash
node test_user_management.js
```

## 📊 角色映射

数据库中的 `sys_access` 字段与前端显示的角色映射：
- `admin` → Administrator
- `guest` → Developer
- `owner` → Administrator

## 🔄 数据同步

- 所有操作都直接与数据库交互
- 前端操作后自动刷新列表
- 支持实时数据更新

## 🎯 主要改进

相比之前的模拟数据版本：
1. **真实数据**: 直接从MySQL数据库获取用户数据
2. **完整CRUD**: 支持完整的增删改查操作
3. **数据验证**: 用户名、邮箱唯一性检查
4. **密码加密**: 使用bcryptjs加密存储密码
5. **错误处理**: 完善的错误处理和用户提示
6. **性能优化**: 支持分页、搜索、筛选等功能

## 🚨 注意事项

1. 确保数据库连接配置正确
2. 管理员权限验证依赖现有的认证系统
3. 删除用户操作不可恢复，请谨慎操作
4. 密码字段在编辑时不显示，如需修改密码请单独实现

## 📝 后续扩展

可以考虑添加的功能：
- 批量操作（批量删除、批量启用/禁用）
- 用户导入/导出
- 密码重置功能
- 用户活动日志
- 更详细的用户信息字段
