const express = require('express');
const { verifyToken, requireRole } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');
const pool = require('../db');

const router = express.Router();

// 角色映射函数
const mapRole = (sysAccess) => {
  const roleMap = {
    'admin': 'Administrator',
    'owner': 'Administrator',
    'guest': 'Developer'
  };
  return roleMap[sysAccess] || 'Developer';
};

// 状态映射函数
const mapStatus = (enabled) => {
  return enabled ? 'Active' : 'Inactive';
};

// 格式化用户数据
const formatUserData = (user) => {
  const fullName = [user.first_name, user.last_name].filter(Boolean).join(' ') || user.username;
  return {
    id: user.id,
    name: fullName,
    username: user.username,
    email: user.email,
    role: mapRole(user.sys_access),
    status: mapStatus(user.enabled),
    dateJoined: user.created_at ? user.created_at.toISOString().split('T')[0] : '',
    phone: user.phone,
    keycloak_id: user.keycloak_id,
    sys_access: user.sys_access,
    enabled: user.enabled,
    first_name: user.first_name,
    last_name: user.last_name
  };
};

// 获取用户列表
router.get('/users', requireRole('admin'), asyncHandler(async (req, res) => {
  const {
    page = 1,
    pageSize = 10,
    search = '',
    role = '',
    status = '',
    sortField = 'created_at',
    sortOrder = 'desc'
  } = req.query;

  try {
    // 构建查询条件
    let whereConditions = [];
    let queryParams = [];

    // 搜索条件
    if (search) {
      whereConditions.push('(username LIKE ? OR email LIKE ? OR first_name LIKE ? OR last_name LIKE ?)');
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern, searchPattern);
    }

    // 角色过滤
    if (role) {
      const sysAccessMap = {
        'Administrator': ['admin', 'owner'],
        'Developer': ['guest'],
        'Manager': ['guest'] // 可以根据需要调整
      };
      if (sysAccessMap[role]) {
        whereConditions.push(`sys_access IN (${sysAccessMap[role].map(() => '?').join(',')})`);
        queryParams.push(...sysAccessMap[role]);
      }
    }

    // 状态过滤
    if (status) {
      const enabledValue = status === 'Active' ? 1 : 0;
      whereConditions.push('enabled = ?');
      queryParams.push(enabledValue);
    }

    // 构建WHERE子句
    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 排序字段映射
    const sortFieldMap = {
      'dateJoined': 'created_at',
      'name': 'username',
      'role': 'sys_access',
      'status': 'enabled'
    };
    const dbSortField = sortFieldMap[sortField] || 'created_at';
    const dbSortOrder = sortOrder === 'asc' ? 'ASC' : 'DESC';

    // 查询总数
    const countQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`;
    const countResult = await new Promise((resolve, reject) => {
      pool.query(countQuery, queryParams, (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    const total = countResult[0].total;

    // 查询用户数据
    const offset = (parseInt(page) - 1) * parseInt(pageSize);
    const dataQuery = `
      SELECT id, username, email, phone, sys_access, keycloak_id, enabled,
             first_name, last_name, created_at, updated_at
      FROM users
      ${whereClause}
      ORDER BY ${dbSortField} ${dbSortOrder}
      LIMIT ? OFFSET ?
    `;

    const dataParams = [...queryParams, parseInt(pageSize), offset];
    const users = await new Promise((resolve, reject) => {
      pool.query(dataQuery, dataParams, (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    // 格式化用户数据
    const formattedUsers = users.map(formatUserData);

    res.json({
      success: true,
      data: {
        users: formattedUsers,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(pageSize),
          total,
          totalPages: Math.ceil(total / parseInt(pageSize))
        }
      }
    });

  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      error: error.message
    });
  }
}));

// 获取单个用户信息
router.get('/users/:id', requireRole('admin'), asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    const query = `
      SELECT id, username, email, phone, sys_access, keycloak_id, enabled,
             first_name, last_name, created_at, updated_at
      FROM users
      WHERE id = ?
    `;

    const result = await new Promise((resolve, reject) => {
      pool.query(query, [id], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    if (result.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    const user = formatUserData(result[0]);

    res.json({
      success: true,
      data: user
    });

  } catch (error) {
    console.error('获取用户详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户详情失败',
      error: error.message
    });
  }
}));

// 创建用户
router.post('/users', requireRole('admin'), asyncHandler(async (req, res) => {
  const { name, username, email, role, phone, password = 'defaultPassword123' } = req.body;

  // 简单验证
  if (!username || !email || !role) {
    return res.status(400).json({
      success: false,
      message: '请填写所有必填字段'
    });
  }

  try {
    // 检查用户名和邮箱是否已存在
    const checkQuery = 'SELECT id FROM users WHERE username = ? OR email = ?';
    const existingUsers = await new Promise((resolve, reject) => {
      pool.query(checkQuery, [username, email], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    if (existingUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: '用户名或邮箱已存在'
      });
    }

    // 角色映射
    const roleMap = {
      'Administrator': 'admin',
      'Developer': 'guest',
      'Manager': 'guest'
    };
    const sysAccess = roleMap[role] || 'guest';

    // 解析姓名
    const nameParts = (name || username).split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    // 密码加密
    const bcrypt = require('bcryptjs');
    const hashedPassword = await bcrypt.hash(password, 10);

    // 插入新用户
    const insertQuery = `
      INSERT INTO users (username, password, email, phone, sys_access, enabled, first_name, last_name)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await new Promise((resolve, reject) => {
      pool.query(insertQuery, [username, hashedPassword, email, phone, sysAccess, true, firstName, lastName], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    // 获取新创建的用户信息
    const newUserId = result.insertId;
    const getUserQuery = `
      SELECT id, username, email, phone, sys_access, keycloak_id, enabled,
             first_name, last_name, created_at, updated_at
      FROM users
      WHERE id = ?
    `;

    const newUserResult = await new Promise((resolve, reject) => {
      pool.query(getUserQuery, [newUserId], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    const newUser = formatUserData(newUserResult[0]);

    res.json({
      success: true,
      data: newUser,
      message: '用户创建成功'
    });

  } catch (error) {
    console.error('创建用户失败:', error);
    res.status(500).json({
      success: false,
      message: '创建用户失败',
      error: error.message
    });
  }
}));

// 更新用户信息
router.put('/users/:id', requireRole('admin'), asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { name, username, email, role, phone, enabled } = req.body;

  try {
    // 检查用户是否存在
    const checkQuery = 'SELECT id FROM users WHERE id = ?';
    const existingUser = await new Promise((resolve, reject) => {
      pool.query(checkQuery, [id], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    if (existingUser.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 检查用户名和邮箱是否被其他用户使用
    if (username || email) {
      const duplicateQuery = 'SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?';
      const duplicateUsers = await new Promise((resolve, reject) => {
        pool.query(duplicateQuery, [username || '', email || '', id], (err, result) => {
          if (err) reject(err);
          else resolve(result);
        });
      });

      if (duplicateUsers.length > 0) {
        return res.status(400).json({
          success: false,
          message: '用户名或邮箱已被其他用户使用'
        });
      }
    }

    // 构建更新字段
    const updateFields = [];
    const updateValues = [];

    if (username) {
      updateFields.push('username = ?');
      updateValues.push(username);
    }
    if (email) {
      updateFields.push('email = ?');
      updateValues.push(email);
    }
    if (phone !== undefined) {
      updateFields.push('phone = ?');
      updateValues.push(phone);
    }
    if (role) {
      const roleMap = {
        'Administrator': 'admin',
        'Developer': 'guest',
        'Manager': 'guest'
      };
      const sysAccess = roleMap[role] || 'guest';
      updateFields.push('sys_access = ?');
      updateValues.push(sysAccess);
    }
    if (enabled !== undefined) {
      updateFields.push('enabled = ?');
      updateValues.push(enabled);
    }
    if (name) {
      const nameParts = name.split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';
      updateFields.push('first_name = ?', 'last_name = ?');
      updateValues.push(firstName, lastName);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有提供要更新的字段'
      });
    }

    updateFields.push('updated_at = NOW()');
    updateValues.push(id);

    const updateQuery = `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`;

    await new Promise((resolve, reject) => {
      pool.query(updateQuery, updateValues, (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    res.json({
      success: true,
      message: '用户信息更新成功'
    });

  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.status(500).json({
      success: false,
      message: '更新用户信息失败',
      error: error.message
    });
  }
}));

// 删除用户
router.delete('/users/:id', requireRole('admin'), asyncHandler(async (req, res) => {
  const { id } = req.params;

  try {
    // 检查用户是否存在
    const checkQuery = 'SELECT id, username FROM users WHERE id = ?';
    const existingUser = await new Promise((resolve, reject) => {
      pool.query(checkQuery, [id], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    if (existingUser.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 删除用户（注意：这会级联删除相关的会话和日志）
    const deleteQuery = 'DELETE FROM users WHERE id = ?';
    await new Promise((resolve, reject) => {
      pool.query(deleteQuery, [id], (err, result) => {
        if (err) reject(err);
        else resolve(result);
      });
    });

    res.json({
      success: true,
      message: '用户删除成功'
    });

  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({
      success: false,
      message: '删除用户失败',
      error: error.message
    });
  }
}));

module.exports = router;
