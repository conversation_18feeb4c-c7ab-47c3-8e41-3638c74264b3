/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */
export default [
  {
    path: '/user',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: './User/Login',
      },
      {
        name: 'register',
        path: '/user/register',
        component: './User/Register',
      },
      {
        name: 'register-result',
        path: '/user/register-result',
        component: './User/register-result',
      },
      {
        name: 'test-navigation',
        path: '/user/test-navigation',
        component: './User/test-navigation',
      },
    ],
  },
  {
    path: '/auth/callback',
    layout: false,
    component: './auth/callback',
  },
  {
    path: '/auth/process-callback',
    layout: false,
    component: './auth/process-callback',
  },
  {
    path: '/auth/logout-callback',
    layout: false,
    component: './auth/logout-callback',
  },
  //控制台
  {
    path: '/Console',
    name: 'console',
    icon: 'ConsoleSqlOutlined',
    routes: [
      {
        path: '/Console/projects',
        name: 'projects',
        component: './Console/projects',
        // access: 'isAdmin',
      },
      {
        path: '/Console/xpu',
        name: 'xpu',
        component: './Console/xpu',
        // routes: [
        //   {
        //     path: '/XPU_Manager/gpuDashboard/nvgpu',
        //     name: 'NVIDIA GPGPU',
        //     component: './XPU_Manager/gpuDashboard/nvgpu',
        //   },
        //   {
        //     path: '/XPU_Manager/gpuDashboard/intelgpu',
        //     name: 'INTEL GPGPU',
        //     component: './XPU_Manager/gpuDashboard/intelgpu',
        //   },
        //   {
        //     path: '/XPU_Manager/gpuDashboard/lluvatar',
        //     name: 'ILUVATAR GPGPU',
        //     component: './XPU_Manager/gpuDashboard/lluvatar',
        //   },
        // ],
      },
      {
        path: '/Console/plan',
        name: 'plan',
        component: './Console/plan',
        routes: [
          {
            path: '/Console/plan/milestoneDetail',
            name: '里程碑详情',
            component: './Console/milestoneDetail',
            hideInMenu: true,
          },
          {
            path: '/Console/plan/newMilestone',
            name: '新建里程碑',
            component: './Console/newMilestone',
            hideInMenu: true,
          },
          {
            path: '/Console/plan/pipelineDetail',
            name: '流水线详情',
            component: './Console/pipelineDetail',
            hideInMenu: true,
          },

        ]
      },

    ],
  },
  //项目管理
  {
    path: '/repository',
    name: 'repository',
    icon: 'ProjectOutlined',
    component: './repository',
    // routes: [
    //   {
    //     path: '/tools/repo/branch',
    //     name: '分支',
    //     component: './repository/branch',
    //   },
    //   {
    //     path: '/tools/repo/commits',
    //     name: '提交',
    //     component: './repository/commits',
    //   },
    //   {
    //     path: '/tools/repo/compare',
    //     name: '比较修订版本',
    //     component: './repository/compare',
    //   },
    // ],
  },
  //工具套件
  {
    path: '/tools',
    name: 'tools',
    icon: 'ToolOutlined',
    routes: [
      {
        path: '/tools/modeling',
        name: 'modeling',
        component: './tools/modeling',
      },
      {
        path: '/tools/s2s',
        name: 's2s',
        component: './tools/s2s',
        // microApp:'IDE_style',
      },
      {
        path: '/tools/ide',
        name: 'ide',
        component: './tools/ide/ide',
      },
      {
        path: '/tools/repo',
        name: 'repo',
        component: '../layouts/RepoLayout',
        routes: [
          {
            path: '/tools/repo',
            component: './tools/repo',
          },
          {
            path: '/tools/repo/newProject',
            component: './tools/repo/newProject',
          },
          {
            path: '/tools/repo/branch',
            component: './tools/repo/branch',
          },
          {
            path: '/tools/repo/commits',
            component: './tools/repo/commits',
          },
          {
            path: '/tools/repo/compare',
            component: './tools/repo/compare',
          },
          {
            path: '/tools/repo/files',
            component: './tools/repo/files',
          },
          {
            path: '/tools/repo/files/newFile',
            component: './tools/repo/newFile',
          },
          {
            path: '/tools/repo/newTag',
            component: './tools/repo/newTag',
          },
          {
            path: '/tools/repo/compare/compare_result',
            component: './tools/repo/compare_result',
          },
          {
            path: '/tools/repo/tags',
            component: './tools/repo/tags',
          },
          {
            path: '/tools/repo/commits/newMergeRequest',
            component: './tools/repo/newMergeRequest',
          },
          {
            path: '/tools/repo/mergeRequests',
            component: './tools/repo/mergeRequests',
          },
          {
            path: '/tools/repo/newMergeRequest',
            component: './tools/repo/newMergeRequest',
          },
          {
            path: '/tools/repo/settings',
            component: './tools/repo/settings',
          },

        ],
      },
      {
        path: '/tools/build',
        name: 'build',
        component: './tools/build',
      },
      {
        path: '/tools/file',
        name: 'file',
        component: './tools/file',
      },
    ],
  },
  //ci/cd
  {
    path: '/ci',
    icon: 'LineChartOutlined',
    name: 'ci',
    component: './ci',
  },
  //deploy
  {
    path: '/deploy',
    name: 'deploy',
    icon: 'DeploymentUnitOutlined',
    routes: [
      {
        path: '/deploy/resource',
        name: 'resource',
        component: './deploy/resource',
      },
      {
        path: '/deploy/plan',
        name: 'plan',
        component: './deploy/plan',
        // microApp:'IDE_style',
      },
      {
        path: '/deploy/tools',
        name: 'tools',
        component: './deploy/tools',
      },
      {
        path: '/deploy/migration',
        name: 'migration',
        component: './deploy/migration',
        // microApp:'IDE_style',
      },
      {
        path: '/deploy/tasks',
        name: 'tasks',
        component: './deploy/tasks',
      },
    ],
  },
  //应用商店
  {
    path: '/appstore',
    name: 'appstore',
    icon: 'AppstoreOutlined',
    component: './appstore',
  },
  {
    path: '/help',
    name: 'help',
    icon: 'DesktopOutlined',
    component: './help',
  },
  //admin
  {
    path: '/admin',
    name: 'admin',
    icon: 'UserOutlined',
    // access: 'isAdmin', // 临时注释掉权限检查
    component: './admin/simple',
  },
  //account
  // {
  //   path: '/account',
  //   name: '账户管理',
  //   icon: 'DesktopOutlined',
  //   component: './account/center',
  // },
  {
    path: '/',
    redirect: '/Console/projects',
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
];
