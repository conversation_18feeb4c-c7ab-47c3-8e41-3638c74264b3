import React, { useState, useEffect } from 'react';
import {
  Button,
  Tag,
  Space,
  Input,
  Select,
  message,
  Modal,
  Form,
  Table,
  Card,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

const { Option } = Select;

interface UserItem {
  id: number;
  name: string;
  username: string;
  email: string;
  role: string;
  status: string;
  dateJoined: string;
  avatar?: string;
}

const UserManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<UserItem[]>([]);
  const [total, setTotal] = useState(0);
  const [searchText, setSearchText] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState<UserItem | null>(null);
  const [form] = Form.useForm();

  // 从API获取用户数据
  const fetchUsers = async (params = {}) => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/users?' + new URLSearchParams({
        page: '1',
        pageSize: '10',
        search: searchText,
        role: roleFilter,
        status: statusFilter,
        ...params
      }), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setUsers(data.data.users);
        setTotal(data.data.pagination.total);
      } else {
        message.error(data.message || '获取用户列表失败');
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [searchText, roleFilter, statusFilter]);

  // 处理编辑用户
  const handleEdit = (record: UserItem) => {
    setCurrentUser(record);
    form.setFieldsValue(record);
    setEditModalVisible(true);
  };

  // 处理删除用户
  const handleDelete = (record: UserItem) => {
    Modal.confirm({
      title: 'Confirm Delete',
      content: `Are you sure you want to delete user ${record.name}?`,
      onOk: async () => {
        try {
          const response = await fetch(`/api/admin/users/${record.id}`, {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include'
          });

          const data = await response.json();

          if (data.success) {
            message.success('User deleted successfully');
            fetchUsers(); // 重新获取用户列表
          } else {
            message.error(data.message || 'Delete failed');
          }
        } catch (error) {
          console.error('删除用户失败:', error);
          message.error('Delete failed');
        }
      },
    });
  };

  // 操作菜单
  const getActionMenu = (record: UserItem): MenuProps => ({
    items: [
      {
        key: 'edit',
        label: '编辑',
        icon: <EditOutlined />,
        onClick: () => handleEdit(record),
      },
      {
        key: 'delete',
        label: '删除',
        icon: <DeleteOutlined />,
        danger: true,
        onClick: () => handleDelete(record),
      },
    ],
  });

  const columns: ColumnsType<UserItem> = [
    {
      title: 'User',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500, fontSize: '14px' }}>{text}</div>
        </div>
      ),
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
      render: (text) => (
        <span style={{ color: '#8c8c8c', fontSize: '14px' }}>{text}</span>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag
          style={{
            backgroundColor: status === 'Active' ? '#f6ffed' : '#fff2f0',
            color: status === 'Active' ? '#52c41a' : '#ff4d4f',
            border: `1px solid ${status === 'Active' ? '#b7eb8f' : '#ffccc7'}`,
            borderRadius: '4px',
            padding: '2px 8px',
            fontSize: '12px'
          }}>
          {status}
        </Tag>
      ),
    },
    {
      title: 'Date Joined',
      dataIndex: 'dateJoined',
      key: 'dateJoined',
      render: (text) => <span style={{ color: '#8c8c8c', fontSize: '14px' }}>{text}</span>,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space split={<span style={{ color: '#d9d9d9' }}>|</span>}>
          <Button type="link" size="small" onClick={() => handleEdit(record)} style={{ padding: 0, fontSize: '14px' }}>
            Edit
          </Button>
          <Button type="link" size="small" danger onClick={() => handleDelete(record)} style={{ padding: 0, fontSize: '14px' }}>
            Delete
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <Card style={{ borderRadius: '8px' }}>
        {/* 页面标题和添加按钮 */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <h1 style={{ fontSize: '24px', fontWeight: 600, margin: 0 }}>User Management</h1>
          <Button type="primary" icon={<PlusOutlined />} onClick={() => setAddModalVisible(true)}>
            Add User
          </Button>
        </div>

        {/* 搜索和筛选区域 */}
        <div style={{ marginBottom: '24px' }}>
          <div style={{ display: 'flex', gap: '12px', alignItems: 'center', flexWrap: 'wrap' }}>
            <Input
              placeholder="Search users"
              allowClear
              style={{ width: 300 }}
              prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
            />
            <Select
              placeholder="Role"
              allowClear
              style={{ width: 120 }}
              value={roleFilter}
              onChange={setRoleFilter}
              suffixIcon={<span style={{ color: '#bfbfbf' }}>▼</span>}
            >
              <Option value="Administrator">Administrator</Option>
              <Option value="Developer">Developer</Option>
              <Option value="Manager">Manager</Option>
            </Select>
            <Select
              placeholder="Status"
              allowClear
              style={{ width: 120 }}
              value={statusFilter}
              onChange={setStatusFilter}
              suffixIcon={<span style={{ color: '#bfbfbf' }}>▼</span>}
            >
              <Option value="Active">Active</Option>
              <Option value="Inactive">Inactive</Option>
            </Select>
            <Select
              placeholder="Date Joined"
              allowClear
              style={{ width: 120 }}
              suffixIcon={<span style={{ color: '#bfbfbf' }}>▼</span>}
            >
              <Option value="desc">Newest</Option>
              <Option value="asc">Oldest</Option>
            </Select>
          </div>
        </div>

        {/* 用户表格 */}
        <Table<UserItem>
          columns={columns}
          dataSource={users}
          rowKey="id"
          pagination={{
            total,
            pageSize: 10,
            showSizeChanger: false,
            showQuickJumper: false,
            showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
          }}
          loading={loading}
          style={{ backgroundColor: '#fff' }}
        />
      </Card>

      {/* 添加用户模态框 */}
      <Modal
        title="Add User"
        open={addModalVisible}
        onCancel={() => setAddModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={async (values) => {
            try {
              const response = await fetch('/api/admin/users', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify(values)
              });

              const data = await response.json();

              if (data.success) {
                message.success('User added successfully');
                setAddModalVisible(false);
                form.resetFields();
                fetchUsers(); // 重新获取用户列表
              } else {
                message.error(data.message || 'Add user failed');
              }
            } catch (error) {
              console.error('添加用户失败:', error);
              message.error('Add user failed');
            }
          }}
        >
          <Form.Item name="name" label="Name" rules={[{ required: true, message: 'Please enter name' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="username" label="Username" rules={[{ required: true, message: 'Please enter username' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="email" label="Email" rules={[{ required: true, type: 'email', message: 'Please enter valid email' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="phone" label="Phone">
            <Input />
          </Form.Item>
          <Form.Item name="password" label="Password" rules={[{ required: true, message: 'Please enter password' }]}>
            <Input.Password />
          </Form.Item>
          <Form.Item name="role" label="Role" rules={[{ required: true, message: 'Please select role' }]}>
            <Select>
              <Option value="Administrator">Administrator</Option>
              <Option value="Developer">Developer</Option>
              <Option value="Manager">Manager</Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Confirm
              </Button>
              <Button onClick={() => setAddModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑用户模态框 */}
      <Modal
        title="Edit User"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={async (values) => {
            try {
              const response = await fetch(`/api/admin/users/${currentUser?.id}`, {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json',
                },
                credentials: 'include',
                body: JSON.stringify(values)
              });

              const data = await response.json();

              if (data.success) {
                message.success('User updated successfully');
                setEditModalVisible(false);
                fetchUsers(); // 重新获取用户列表
              } else {
                message.error(data.message || 'Update user failed');
              }
            } catch (error) {
              console.error('更新用户失败:', error);
              message.error('Update user failed');
            }
          }}
        >
          <Form.Item name="name" label="Name" rules={[{ required: true, message: 'Please enter name' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="username" label="Username" rules={[{ required: true, message: 'Please enter username' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="email" label="Email" rules={[{ required: true, type: 'email', message: 'Please enter valid email' }]}>
            <Input />
          </Form.Item>
          <Form.Item name="phone" label="Phone">
            <Input />
          </Form.Item>
          <Form.Item name="role" label="Role" rules={[{ required: true, message: 'Please select role' }]}>
            <Select>
              <Option value="Administrator">Administrator</Option>
              <Option value="Developer">Developer</Option>
              <Option value="Manager">Manager</Option>
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Confirm
              </Button>
              <Button onClick={() => setEditModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement;