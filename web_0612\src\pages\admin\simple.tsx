import React from 'react';
import { Card, Button, Table } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

const SimpleUserManagement: React.FC = () => {
  console.log('SimpleUserManagement component rendering...');

  const mockData = [
    {
      id: 1,
      name: 'Test User',
      role: 'Administrator',
      status: 'Active',
      dateJoined: '2023-01-01'
    }
  ];

  const columns = [
    {
      title: 'User',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Role',
      dataIndex: 'role',
      key: 'role',
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
    },
    {
      title: 'Date Joined',
      dataIndex: 'dateJoined',
      key: 'dateJoined',
    },
  ];

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <Card style={{ borderRadius: '8px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <h1 style={{ fontSize: '24px', fontWeight: 600, margin: 0 }}>User Management (Simple)</h1>
          <Button type="primary" icon={<PlusOutlined />}>
            Add User
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={mockData}
          rowKey="id"
          pagination={false}
        />
      </Card>
    </div>
  );
};

export default SimpleUserManagement;
