-- 插入示例用户数据
USE xhdb_test;

-- 清除现有用户数据（除了admin）
DELETE FROM users WHERE username != 'admin';

-- 插入示例用户数据
INSERT INTO users (username, password, email, phone, sys_access, enabled, first_name, last_name, created_at) VALUES
('ethan.harper', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '<EMAIL>', '******-0101', 'admin', true, '<PERSON>', '<PERSON>', '2023-01-15 10:30:00'),
('olivia.bennett', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '<EMAIL>', '******-0102', 'guest', true, '<PERSON>', '<PERSON>', '2023-02-20 14:15:00'),
('liam.carter', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '<EMAIL>', '******-0103', 'guest', false, 'Liam', 'Carter', '2023-03-10 09:45:00'),
('sophia.davis', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '<EMAIL>', '******-0104', 'guest', true, 'Sophia', 'Davis', '2023-04-05 16:20:00'),
('noah.evans', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '<EMAIL>', '******-0105', 'admin', true, 'Noah', 'Evans', '2023-05-12 11:10:00'),
('ava.foster', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '<EMAIL>', '******-0106', 'guest', true, 'Ava', 'Foster', '2023-06-18 13:30:00'),
('jackson.gray', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '<EMAIL>', '******-0107', 'guest', false, 'Jackson', 'Gray', '2023-07-22 08:45:00'),
('isabella.hayes', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '<EMAIL>', '******-0108', 'admin', true, 'Isabella', 'Hayes', '2023-08-30 15:25:00'),
('lucas.ingram', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '<EMAIL>', '******-0109', 'guest', true, 'Lucas', 'Ingram', '2023-09-15 12:40:00'),
('mia.jenkins', '$2b$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', '<EMAIL>', '******-0110', 'guest', true, 'Mia', 'Jenkins', '2023-10-20 17:55:00');

-- 验证插入的数据
SELECT 
    id, 
    username, 
    email, 
    phone, 
    sys_access, 
    enabled, 
    CONCAT(first_name, ' ', last_name) as full_name,
    created_at 
FROM users 
ORDER BY created_at DESC;

-- 显示用户统计
SELECT 
    sys_access as role,
    COUNT(*) as total_users,
    SUM(CASE WHEN enabled = 1 THEN 1 ELSE 0 END) as active_users,
    SUM(CASE WHEN enabled = 0 THEN 1 ELSE 0 END) as inactive_users
FROM users 
GROUP BY sys_access;
