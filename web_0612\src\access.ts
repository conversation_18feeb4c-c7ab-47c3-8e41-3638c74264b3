/**
 * @see https://umijs.org/docs/max/access#access
 * */
export default function access(initialState: { currentUser?: API.CurrentUser } | undefined) {
  const { currentUser } = initialState ?? {};

  // 定义管理员角色列表
  const adminRoles = ['owner', 'admin'];

  // 检查用户是否具有管理员权限
  const isAdmin = Boolean(
    currentUser && (
      // 检查 access 字段
      adminRoles.includes(currentUser.sys_access) ||
      // 检查 roles 数组（如果存在）
      (currentUser.roles && currentUser.roles.some(role => adminRoles.includes(role)))
    )
  );

  return {
    isAdmin,
    // 可以添加更细粒度的权限控制
    canManageProjects: isAdmin,
    canManageDeploy: isAdmin,
    canManageNodes: isAdmin,
  };
}
