const axios = require('axios');

// 测试用户管理API
async function testUserManagementAPI() {
  const baseURL = 'http://localhost:8001/api/admin';

  try {
    console.log('🧪 Testing User Management API...');

    // 测试获取用户列表
    console.log('\n📋 Testing GET /users...');
    const response = await axios.get(`${baseURL}/users`, {
      params: {
        page: 1,
        pageSize: 10,
        search: '',
        role: '',
        status: ''
      },
      withCredentials: true
    });

    console.log('✅ Users list response:');
    console.log('- Success:', response.data.success);
    console.log('- Total users:', response.data.data?.pagination?.total);
    console.log('- Users count:', response.data.data?.users?.length);

    if (response.data.data?.users?.length > 0) {
      console.log('- First user:', response.data.data.users[0]);

      // 测试获取单个用户
      const userId = response.data.data.users[0].id;
      console.log(`\n👤 Testing GET /users/${userId}...`);
      const userResponse = await axios.get(`${baseURL}/users/${userId}`, {
        withCredentials: true
      });

      console.log('✅ Single user response:', userResponse.data);
    }

    // 测试创建用户
    console.log('\n➕ Testing POST /users...');
    const newUser = {
      name: 'Test User',
      username: 'test.user',
      email: '<EMAIL>',
      phone: '******-9999',
      role: 'Developer',
      password: 'testPassword123'
    };

    const createResponse = await axios.post(`${baseURL}/users`, newUser, {
      withCredentials: true
    });

    console.log('✅ Create user response:', createResponse.data);

  } catch (error) {
    console.error('❌ API Test failed:');
    if (error.response) {
      console.error('- Status:', error.response.status);
      console.error('- Data:', error.response.data);
    } else {
      console.error('- Error:', error.message);
    }
  }
}

// 测试数据库连接
async function testDatabaseConnection() {
  try {
    console.log('🔌 Testing database connection...');
    const response = await axios.get('http://localhost:8001/health');
    console.log('✅ Server health check:', response.data);
  } catch (error) {
    console.error('❌ Database connection test failed:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  (async () => {
    await testDatabaseConnection();
    await testUserManagementAPI();
  })();
}

module.exports = { testUserManagementAPI, testDatabaseConnection };
